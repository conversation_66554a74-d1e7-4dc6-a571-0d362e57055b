import { TasksService } from "./tasks.service";
import type { CreateTaskDto } from "./dto/create-task.dto";
import type { UpdateTaskDto } from "./dto/update-task.dto";
export declare class TasksController {
    private readonly tasksService;
    constructor(tasksService: TasksService);
    create(createTaskDto: CreateTaskDto): Promise<{
        taskId: string;
        status: string;
        createdAt: Date;
        taskName: string;
        taskType: import("./dto/create-task.dto").TaskType;
        priority: import("./dto/create-task.dto").TaskPriority;
        scheduledTime?: string;
        createdBy: string;
        robotId: string;
        zoneId: string;
        estimatedDuration?: number;
    }>;
    findAll(userId?: string, robotId?: string): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateTaskDto: UpdateTaskDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: string): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
