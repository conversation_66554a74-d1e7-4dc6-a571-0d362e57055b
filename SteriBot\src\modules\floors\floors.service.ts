import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateFloorDto } from "./dto/create-floor.dto"
import type { UpdateFloorDto } from "./dto/update-floor.dto"

@Injectable()
export class FloorsService {
  private readonly collection = "floors"

  constructor(private firebaseService: FirebaseService) {}

  async create(createFloorDto: CreateFloorDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const floorData = {
      ...createFloorDto,
      floorId: docRef.id,
      isActive: true,
      createdAt: new Date(),
    }

    await docRef.set(floorData)
    return floorData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByBuilding(buildingId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where("buildingId", "==", buildingId).get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Floor with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateFloorDto: UpdateFloorDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateFloorDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Floor deleted successfully" }
  }
}
