import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsBoolean, IsEnum } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export enum RobotStatus {
  IDLE = 'idle',
  WORKING = 'working',
  CHARGING = 'charging',
  MAINTENANCE = 'maintenance',
  ERROR = 'error'
}

export class UpdateRobotStatusDto {
  @ApiProperty({ enum: RobotStatus, required: false })
  @IsOptional()
  @IsEnum(RobotStatus)
  status?: RobotStatus

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  batteryLevel?: number

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currentPosition?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isConnected?: boolean

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currentTaskId?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  errorMessage?: string
}
