{"name": "steribot-backend", "version": "0.1.0", "private": true, "scripts": {"build": "nest build", "dev": "nest start --watch", "start": "nest start", "start:dev": "nest start --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "latest", "@nestjs/config": "latest", "@nestjs/core": "latest", "@nestjs/microservices": "latest", "@nestjs/platform-express": "latest", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "latest", "class-transformer": "latest", "class-validator": "latest", "firebase-admin": "^13.4.0", "reflect-metadata": "latest", "rxjs": "latest", "zod": "^3.24.1"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/node": "^22", "typescript": "^5"}}