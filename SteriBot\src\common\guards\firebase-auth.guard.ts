import { Injectable, type CanActivate, type ExecutionContext, UnauthorizedException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"

@Injectable()
export class FirebaseAuthGuard implements CanActivate {
  constructor(private firebaseService: FirebaseService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const token = this.extractTokenFromHeader(request)

    if (!token) {
      throw new UnauthorizedException("No token provided")
    }

    try {
      // Check if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development'

      if (isDevelopment) {
        // Development mode: decode mock token
        try {
          const decodedMockToken = JSON.parse(Buffer.from(token, 'base64').toString())

          // Validate token structure and expiration
          if (!decodedMockToken.uid || !decodedMockToken.email || !decodedMockToken.exp) {
            throw new Error("Invalid mock token structure")
          }

          if (Date.now() > decodedMockToken.exp) {
            throw new Error("Mock token expired")
          }

          request.user = {
            uid: decodedMockToken.uid,
            email: decodedMockToken.email,
            role: decodedMockToken.role
          }
          return true
        } catch (mockError) {
          console.log(`[DEBUG] Mock token validation failed: ${mockError.message}`)
          throw new UnauthorizedException("Invalid development token")
        }
      } else {
        // Production mode: For testing, we'll decode the custom token manually
        // In a real production app, the client would exchange the custom token for an ID token
        try {
          // Try to verify as ID token first
          const decodedToken = await this.firebaseService.getAuth().verifyIdToken(token)
          request.user = decodedToken
          return true
        } catch (idTokenError) {
          // If that fails, try to decode the custom token manually for testing
          try {
            // Custom tokens are JWT tokens - let's decode them manually
            const jwt = require('jsonwebtoken')
            const decoded = jwt.decode(token)

            if (decoded && decoded.uid) {
              request.user = {
                uid: decoded.uid,
                email: decoded.email || decoded.claims?.email,
                role: decoded.role || decoded.claims?.role,
                username: decoded.username || decoded.claims?.username
              }
              console.log(`[DEBUG] Custom token decoded successfully for user: ${request.user.email}`)
              return true
            } else {
              throw new Error("Invalid custom token structure")
            }
          } catch (customTokenError) {
            console.log(`[DEBUG] Token verification failed: ID token error: ${idTokenError.message}, Custom token error: ${customTokenError.message}`)
            throw new UnauthorizedException("Invalid token")
          }
        }
      }
    } catch (error) {
      console.log(`[DEBUG] Auth guard error: ${error.message}`)
      throw new UnauthorizedException("Invalid token")
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(" ") ?? []
    return type === "Bearer" ? token : undefined
  }
}
