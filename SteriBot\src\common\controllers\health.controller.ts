import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOperation } from '@nestjs/swagger'
import { FirebaseService } from '../../config/firebase/firebase.service'

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly firebaseService: FirebaseService) {}

  @Get()
  @ApiOperation({ summary: 'Health check endpoint' })
  async getHealth() {
    const isFirebaseHealthy = await this.firebaseService.isHealthy()
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      services: {
        firebase: isFirebaseHealthy ? 'healthy' : 'unhealthy',
      },
    }
  }

  @Get('firebase')
  @ApiOperation({ summary: 'Firebase specific health check' })
  async getFirebaseHealth() {
    const isHealthy = await this.firebaseService.isHealthy()
    
    return {
      service: 'firebase',
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
    }
  }
}
