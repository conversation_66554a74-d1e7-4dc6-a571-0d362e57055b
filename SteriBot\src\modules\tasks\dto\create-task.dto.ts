import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsDateString, IsN<PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export enum TaskType {
  MANUAL_DISINFECTION = "manual_disinfection",
  SCHEDULED_DISINFECTION = "scheduled_disinfection",
  NAVIGATION = "navigation",
  MAINTENANCE = "maintenance",
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

export class CreateTaskDto {
  @ApiProperty()
  @IsString()
  taskName: string

  @ApiProperty({ enum: TaskType })
  @IsEnum(TaskType)
  taskType: TaskType

  @ApiProperty({ enum: TaskPriority })
  @IsEnum(TaskPriority)
  priority: TaskPriority

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  scheduledTime?: string

  @ApiProperty()
  @IsString()
  createdBy: string

  @ApiProperty()
  @IsString()
  robotId: string

  @ApiProperty()
  @IsString()
  zoneId: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  estimatedDuration?: number
}
