import { Injectable, UnauthorizedException, BadRequestException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import { UsersService } from "../users/users.service"
import type { RegisterDto } from "./dto/register.dto"
import type { LoginDto } from "./dto/login.dto"
import { UserRole } from "../../common/decorators/roles.decorator"

@Injectable()
export class AuthService {
  constructor(
    private firebaseService: FirebaseService,
    private usersService: UsersService,
  ) {}

  async register(registerDto: RegisterDto) {
    try {
      const auth = this.firebaseService.getAuth()
      let userRecord: any

      if (auth && process.env.NODE_ENV !== 'development') {
        // Create user in Firebase Auth (production mode)
        userRecord = await auth.createUser({
          email: registerDto.email,
          password: registerDto.password,
          displayName: registerDto.username,
        })
      } else {
        // Mock user creation for development mode
        userRecord = {
          uid: `dev_user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          email: registerDto.email,
          displayName: registerDto.username,
        }
      }

      // Ensure role is of type UserRole
      const role: UserRole = registerDto.role ?? UserRole.USER

      // Create user document in Firestore
      const userData = {
        userId: userRecord.uid,
        username: registerDto.username,
        email: registerDto.email,
        role,
        language: registerDto.language || "en",
        createdAt: new Date(),
        // Don't include lastLogin if it's undefined (Firestore doesn't allow undefined values)
      }

      await this.usersService.create(userData)

      return {
        message: "User registered successfully",
        userId: userRecord.uid,
        ...(process.env.NODE_ENV === 'development' && {
          note: "Development mode: User created locally without Firebase Auth"
        })
      }
    } catch (error) {
      // Use BadRequestException for registration errors
      throw new BadRequestException(`Registration failed: ${error.message}`)
    }
  }

  async validateUser(uid: string) {
    try {
      const userRecord = await this.firebaseService.getAuth().getUser(uid)
      const userData = await this.usersService.findById(uid)

      return {
        ...userRecord,
        ...userData,
      }
    } catch (error) {
      throw new UnauthorizedException("User validation failed")
    }
  }

  async updateLastLogin(userId: string) {
    await this.usersService.updateLastLogin(userId)
  }

  async login(loginDto: LoginDto) {
    try {
      const auth = this.firebaseService.getAuth()

      // Check if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development'

      if (isDevelopment) {
        // Development mode: Find user by email and return mock token
        const user = await this.usersService.findByEmail(loginDto.email)
        if (!user) {
          throw new UnauthorizedException("Invalid email or password")
        }

        // In development, we'll create a simple mock token
        const mockToken = Buffer.from(JSON.stringify({
          uid: user.userId,
          email: user.email,
          role: user.role,
          exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
        })).toString('base64')

        return {
          message: "Login successful",
          token: mockToken,
          user: {
            uid: user.userId,
            email: user.email,
            username: user.username,
            role: user.role
          },
          note: "Development mode: Mock token generated"
        }
      } else {
        // Production mode: Find user in Firestore and create custom token
        const user = await this.usersService.findByEmail(loginDto.email)
        if (!user) {
          throw new UnauthorizedException("Invalid email or password")
        }

        // In production, create a custom Firebase token
        if (auth) {
          try {
            const customToken = await auth.createCustomToken(user.userId, {
              email: user.email,
              role: user.role,
              username: user.username
            })

            return {
              message: "Login successful",
              token: customToken,
              user: {
                uid: user.userId,
                email: user.email,
                username: user.username,
                role: user.role
              },
              note: "Production mode: Firebase custom token generated"
            }
          } catch (firebaseError) {
            throw new UnauthorizedException(`Firebase token creation failed: ${firebaseError.message}`)
          }
        } else {
          throw new UnauthorizedException("Firebase Auth not available")
        }
      }
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error
      }
      throw new UnauthorizedException(`Login failed: ${error.message}`)
    }
  }
}
