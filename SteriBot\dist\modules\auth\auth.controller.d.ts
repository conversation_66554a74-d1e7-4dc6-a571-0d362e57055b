import { AuthService } from "./auth.service";
import { RegisterDto } from "./dto/register.dto";
import { LoginDto } from "./dto/login.dto";
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        note: string;
        message: string;
        userId: any;
    }>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        token: string;
        user: {
            uid: any;
            email: any;
            username: any;
            role: any;
        };
        note: string;
    }>;
    getProfile(req: any): Promise<any>;
}
